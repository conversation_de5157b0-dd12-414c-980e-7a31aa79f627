<?php
// Test database connection
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing database connection...<br>";

try {
    include '../includes/config.php';
    echo "Config file included successfully.<br>";
    
    if (isset($pdo)) {
        echo "PDO object exists.<br>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "Database connection successful! Test result: " . $result['test'] . "<br>";
        
        // Test blog_posts table
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM blog_posts");
        $result = $stmt->fetch();
        echo "Blog posts count: " . $result['count'] . "<br>";
        
    } else {
        echo "PDO object not found.<br>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
