<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Blog Path Issues</h2>";

echo "<h3>1. Testing config.php path</h3>";
try {
    include '../../includes/config.php';
    echo "✓ Config loaded successfully<br>";
    if (isset($pdo)) {
        echo "✓ PDO object exists<br>";
    } else {
        echo "✗ PDO object missing<br>";
    }
} catch (Exception $e) {
    echo "✗ Config error: " . $e->getMessage() . "<br>";
}

echo "<h3>2. Testing vendor/autoload.php path</h3>";
if (file_exists('../../vendor/autoload.php')) {
    echo "✓ vendor/autoload.php exists at ../../vendor/autoload.php<br>";
} else {
    echo "✗ vendor/autoload.php not found at ../../vendor/autoload.php<br>";
}

echo "<h3>3. Testing blog.php path</h3>";
if (file_exists('../../blog.php')) {
    echo "✓ blog.php exists at ../../blog.php<br>";
} else {
    echo "✗ blog.php not found at ../../blog.php<br>";
}

echo "<h3>4. Testing includes/main.php path</h3>";
if (file_exists('../../includes/main.php')) {
    echo "✓ includes/main.php exists at ../../includes/main.php<br>";
} else {
    echo "✗ includes/main.php not found at ../../includes/main.php<br>";
}

echo "<h3>5. Current working directory</h3>";
echo "Current directory: " . getcwd() . "<br>";
echo "Script path: " . __FILE__ . "<br>";
?>
